import './publish-sdk';
export const SDK_MODULE_NAME = 'FlSdk';

window.flsdk = {
    _send: null,
    FlSdkInit() {
        /**
        * appid 为平台ID
        * version 版本号 (首包可设置1.0.1为初始版本)
        * gameName 游戏名
        * videoAdUnitID 视频id(无请求接口时默认)
        * isOpenLog 输出日志开关
        */
        let params = {
            "appId": "wx11852d7c7ee38496",
            "version": "1.1.0",
            "gameName": "吃碰杠胡啦",
            "videoAdUnitID": "adunit-a91c155150f006d9",
            // "isOpenLog": true
        };
        /** 初始化sdk */
        window.gameSdk.sdk.init({
            ...params, callback(err, res) {
                if (!err) {
                    console.log(err, res);
                    var abTest = res.sdkConfig.abTest;
                    // 获取后台配置
                    window.gameSdk.sdk.getBackStageConfig({
                        callback(err, res) {
                            console.log("获取配置====", res);
                            window.flsdk.ActiveReport();
                            window.flsdk.Send2Unity("OnFlSdkInit", abTest);
                        }
                    })
                }
            }
        });
    },
    GetAbTest() {
        return "";
    },
    Send2Unity(method, str = '') {
        if (!this._send) {
            this._send = GameGlobal.Module.SendMessage;
        }
        this._send(SDK_MODULE_NAME, method, str);
    },
    ActiveReport() {
        window.gameSdk.sdk.activeReport();
        console.log("[flsdk] activeReport");
    },
    ShowRewardVideoAd(positionTag) {
        console.log("[flsdk] showRewardVideoAd");
        window.gameSdk.sdk.showRewardVideoAd({
            positionTag:positionTag,
            callback: (err, res) => {
                if (!err) {
                    window.flsdk.Send2Unity("OnRewardVideoAdSuccess");
                    console.log("视频观看获取奖励成功1!")
                } else {
                    window.flsdk.Send2Unity("OnRewardVideoAdFail");
                    // 根据游戏内容需求是否需要使用
                    if(res.code == 1001){ 
                        console.log("视频获取显示失败");
                    }else{
                        console.log("未完整观看视频");
                    }
                }
            }
        });
    },
    ShareAppMessage(positionTag) {
        console.log("[flsdk] shareAppMessage:" + positionTag);
        window.gameSdk.sdk.shareAppMessage({
            positionTag: positionTag,
            callback: (err, res) => {
                if (!err) {
                    console.log("分享成功");
                    window.flsdk.Send2Unity("OnShareSuccess");
                } else {
                    console.log("分享失败");
                    window.flsdk.Send2Unity("OnShareFail");
                }
            }
        });
    },
    SendEvent(eventName, data, opt) {
        var reportData = JSON.parse(data);
        if (opt == "") {
            window.gameSdk.sdk.sendEvent(eventName, reportData);
        } else {
            window.gameSdk.sdk.sendEvent(eventName, reportData, opt);
        }
        // console.log("=======report=======",eventName, reportData);
    },
    SetCurrentLevel(level) {
        window.gameSdk.sdk.setCurrentLevel(level);
    },
    VideoExposure(count) {
        window.gameSdk.sdk.videoExposure(count);
    }
}

