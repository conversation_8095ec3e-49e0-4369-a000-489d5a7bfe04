%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 1795359250, guid: 1cf430f187a0b40eda7f668318d8be23, type: 3}
  m_Name: MiniGameConfig
  m_EditorClassIdentifier: 
  ProjectConf:
    projectName: "\u5403\u78B0\u6760\u80E1\u5566"
    Appid: wx11852d7c7ee38496
    CDN: https://www.baidu.com
    assetLoadType: 1
    compressDataPackage: 1
    VideoUrl: 
    DST: E:/ProjectsUnity/MaJiangMerge-fengling/Release/wxgame
    StreamCDN: 
    bundleHashLength: 32
    bundlePathIdentifier: StreamingAssets;
    bundleExcludeExtensions: json;version;
    AssetsUrl: 
    MemorySize: 256
    HideAfterCallMain: 1
    preloadFiles: 
    Orientation: 0
    bgImageSrc: Assets/_MyGame/RawRes/bgText.png
    dataFileSubPrefix: 
    maxStorage: 200
    defaultReleaseSize: 31457280
    texturesHashLength: 8
    texturesPath: Assets/Textures
    needCacheTextures: 1
    loadingBarWidth: 240
    needCheckUpdate: 1
    disableHighPerformanceFallback: 0
  SDKOptions:
    UseFriendRelation: 0
    UseCompressedTexture: 0
    UseMiniGameChat: 0
    PreloadWXFont: 0
  CompileOptions:
    DevelopBuild: 0
    AutoProfile: 0
    ScriptOnly: 0
    Il2CppOptimizeSize: 1
    profilingFuncs: 0
    Webgl2: 0
    fbslim: 1
    DeleteStreamingAssets: 1
    ProfilingMemory: 0
    CleanBuild: 0
    CustomNodePath: 
    showMonitorSuggestModal: 0
    enableProfileStats: 0
    enableRenderAnalysis: 0
    iOSAutoGCInterval: 10000
  CompressTexture:
    halfSize: 0
    useDXT5: 0
    bundleSuffix: bundle
    parallelWithBundle: 0
    bundleDir: 
    dstMinDir: 
    debugMode: 0
    force: 0
  PlayerPrefsKeys:
  - Key_SoundEffectOn
  - Key_VibrationOn
  - Key_GameProgress
  - Key_NeedGuide
  - Key_Num
